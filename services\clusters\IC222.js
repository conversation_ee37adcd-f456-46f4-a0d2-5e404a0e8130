"use strict"
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import md5 from "md5";
import MBClusterManagerBase from "../mbClusterManagerBase";
import utils from "../../utils";

class IC222 extends MBClusterManagerBase {
    constructor (dbConnector) {     
        super(dbConnector);   

    }

    AMG360KM() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0x97 + "," + 0x01 + "," + 0xEF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x07 + "," + 0xD9 + "," + 0x01 + "," + 0x76 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0x20 + "," + 0x16 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"hardreset\" }," +
        "            { \"proc\": \"sleep\", \"param\": 2000}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" +
        "        ]" +
        "}";
        return CMDResponse;
    }

    AMG330KM() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0x97 + "," + 0x01 + "," + 0xEE + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x07 + "," + 0xD9 + "," + 0x01 + "," + 0x12 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0x20 + "," + 0x54 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"hardreset\" }," +
        "            { \"proc\": \"sleep\", \"param\": 2000}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" +
        "        ]" +
        "}";
        return CMDResponse;
    }

    AMGEnable() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0x20 + "," + 0x54 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"hardreset\" }," +
        "            { \"proc\": \"sleep\", \"param\": 2000}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" +
        "        ]" +
        "}";
        return CMDResponse;
    }

    AMGEnable205() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0x20 + "," + 0x16 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0xA1 + "," + 0x01 + "," + 0x20 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"hardreset\" }," +
        "            { \"proc\": \"sleep\", \"param\": 2000}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" +
        "        ]" +
        "}";
        return CMDResponse;
    }

    AMGDisable() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0x97 + "," + 0x01 + "," + 0xEE + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x07 + "," + 0xD9 + "," + 0x01 + "," + 0x12 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"hardreset\" }," +
        "            { \"proc\": \"sleep\", \"param\": 2000}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" +
        "        ]" +
        "}";
        return CMDResponse;
    }


    CheckVirginizable() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0xF1 + "," + 0x00 + "], \"reply\": [" + 0x62 + "," + 0xF1 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0x00 + "," + 0x01 + "], \"reply\": [" + 0x62 + "," + 0x00 + "," + 0x01 + "] }," +
        "            { \"proc\": \"push\" }," +
        "            { \"proc\": \"findaddress\", \"param\": {\"start\":\"0xFEDD7C00\",\"end\":\"0xFEDD7F00\"} }," +
        "            { \"proc\": \"pop\" }," +
        "            { \"proc\": \"checkwritable\" }" +
        "        ]" +
        "}";
        return CMDResponse;
    }   

    VirginMethod3() {
        // 0x3D, 0x14, 0x02, 0x00, 0x01, 0x9D, 0x04, 0xFE, 0xFF, 0xFF, 0xFF
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
// SSID       "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x95 + "," + 0x08 + "," + 0x00 + "," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x9D + "," + 0x04 + "," + 0xFE + "," + 0xFF +"," + 0xFF +"," + 0xFF +"], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0xD9 + "," + 0x01 + "], \"reply\": [" + 0x63 + "] }," +
        "            { \"proc\": \"push\" }," +
        "            { \"proc\": \"setbit\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0xD9+ "," + 0x01 + "], \"mask\": [" + 0x80 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x07 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"clearbit\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0xD9+ "," + 0x01 + "], \"mask\": [" + 0x7F + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"hardreset\" }," +
        "            { \"proc\": \"sleep\", \"param\": 2000}," +
        "            { \"proc\": \"init\"}" +
        "        ]" +
        "}";
        return CMDResponse;
    }    

    VirginMethod2() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0xF0 + "," + 0x0B + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"hardreset\" }," +
        "            { \"proc\": \"sleep\", \"param\": 2000}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x07 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" +
        "        ]" +
        "}";
        return CMDResponse;
    }    

    VirginMethod1() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0xF1 + "," + 0x00 + "], \"reply\": [" + 0x62 + "," + 0xF1 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0x00 + "," + 0x01 + "], \"reply\": [" + 0x62 + "," + 0x00 + "," + 0x01 + "] }," +
        "            { \"proc\": \"push\" }," +
        "            { \"proc\": \"findaddress\", \"param\": {\"start\":\"0xFEDD7C00\",\"end\":\"0xFEDD7F00\"} }," +
        "            { \"proc\": \"pop\" }," +
        "            { \"proc\": \"checkwritable\" }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x95 + "," + 0x08 + "," + 0x00 + "," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x9D + "," + 0x04 + "," + 0xFF + "," + 0xFF +"," + 0xFF +"," + 0xFF +"], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
//        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x91 + "," + 0x04 + "," + 0x00 + "," + 0x00 +"," + 0x00 +"," + 0x00 +"], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0xE2 + "," + 0x02 + "," + 0xFF + "," + 0xFF +"], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x07 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
//        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x08 + "," + 0x01 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
//        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x08 + "," + 0x02 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
//        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x08 + "," + 0x06 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
//        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x15 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"hardreset\" }," +
        "            { \"proc\": \"sleep\", \"param\": 2000}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" +
        "        ]" +
        "}";
        return CMDResponse;
    }   

    EpromEdit() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }" +
        "        ]" +
        "}";        
        return CMDResponse;
    }

    Unlock() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
        "            { \"proc\": \"securityinit\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }" +
        "        ]" +
        "}";        
        return CMDResponse;
    }

}
export default IC222;

